@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom keyframes - moveBackground removed as no longer needed */

/* Ensure backdrop-filter works properly */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    backdrop-filter: blur(10px);
  }
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-neutral-200 {
    border-color: #000;
  }

  .dark .border-neutral-700 {
    border-color: #fff;
  }
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

/* Focus visible improvements */
.focus\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--ring-color, #3b82f6);
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
